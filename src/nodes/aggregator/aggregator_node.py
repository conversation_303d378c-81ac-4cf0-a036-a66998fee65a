import logging
from typing import Literal, Union

import pandas as pd
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command, Send

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.aggregator.aggregator_prompt import aggregator_prompt_template, json_prompt_template
from src.nodes.common.data_center import DataCenter
from src.nodes.common.dimension_config import DimensionManager
from src.nodes.common.types import AggregatorInfo
from src.state import State
from src.zego_tools import generate_sql
from src.zego_tools.sql_executor import execute_sql_for_llm

logger = logging.getLogger(__name__)


async def aggregator_node(
    state: State, config: RunnableConfig, *, store: BaseStore
) -> Command[Literal["__end__"]]:
    """
    聚合节点：收集所有维度分析的结果，生成最终的综合分析结论
    路由逻辑由aggregator_router处理
    """
    logger.info("🤖aggregator node is working.")
    node_name = "aggregator"
    state_update = {"messages": []}

    # 检查是否有维度分析结果
    dimension_analysis_results = state.get("dimension_analysis_results", {})

    # 如果没有任何分析结果，直接结束
    if len(dimension_analysis_results) == 0:
        # 创建默认的聚合分析结果
        default_aggregator_info = AggregatorInfo(
            thinking="未检测到任何维度分析结果，无法进行综合分析。",
            final_conclusion="由于没有任何维度分析数据，分析过程结束。",
            has_critical_issues=False,
            confidence_level="低",
            need_further_analysis=False,
        )
        state_update["messages"].append(default_aggregator_info.to_message())
        state_update.update({"aggregator_info": default_aggregator_info})
        return Command(update=state_update, goto="__end__")

    # 构建提示词
    prompt_list = [
        aggregator_prompt_template.format(CURRENT_TIME=get_prompt_timestamp()),
        json_prompt_template.format(json_schema=AggregatorInfo.model_json_schema()),
    ]
    prompt = "\n".join(prompt_list)

    # 输入消息 - 只使用用户问题、dispatcher分析和各维度结论消息
    input_messages = [*state.get("messages", []), SystemMessage(content=prompt)]

    # 请求LLM
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
        node_name, input_messages, AggregatorInfo
    )
    state_update.update(create_token_update(input_tokens, output_tokens))

    # 处理响应
    aggregator_info: AggregatorInfo = response_parsed
    logger.info(
        f"[{node_name}] ✅aggregator_info = \n{aggregator_info.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    # 使用统一的消息生成方法
    state_update["messages"].append(aggregator_info.to_message())

    # 更新状态
    state_update.update({"aggregator_info": aggregator_info})

    # 如果需要进一步分析，预先执行查询并存储数据
    if aggregator_info.need_further_analysis and aggregator_info.query_params_list:
        logger.info(f"[{node_name}] 需要进一步分析，预先执行 {len(aggregator_info.query_params_list)} 个查询")

        # 执行进一步分析的查询
        await _execute_further_analysis_queries(
            aggregator_info.query_params_list, node_name, store, config
        )

    # 返回状态更新，路由逻辑由aggregator_router处理
    return Command(update=state_update, goto="__end__")


async def _execute_further_analysis_queries(
    query_params_list, node_name: str, store: BaseStore, config: RunnableConfig
) -> None:
    """
    执行进一步分析的查询并存储数据

    Args:
        query_params_list: 查询参数列表
        node_name: 节点名称
        store: 存储对象
        config: 运行配置
    """
    for i, query_params in enumerate(query_params_list):
        try:
            # 生成SQL并执行查询
            sql = generate_sql(query_params)
            result_df, error_info_text = await execute_sql_for_llm(sql)

            # 生成唯一的数据键
            data_key = f"further_analysis_{i}_{query_params.metric_name}_{query_params.analysis_type}"

            # 使用DataCenter存储数据
            DataCenter.store_data(
                store=store,
                config=config,
                data_key=data_key,
                data=result_df,
                error_info=error_info_text,
                query_params=query_params,
                query_title=f"进一步分析: {query_params.metric_name} - {query_params.analysis_type}",
            )

            logger.info(f"[{node_name}] ✅ 进一步分析查询完成: {data_key}, {len(result_df)} 条记录")

        except Exception as e:
            logger.error(f"[{node_name}] ❌ 进一步分析查询失败: {query_params.metric_name} - {e}")
            # 存储空数据和错误信息
            data_key = f"further_analysis_{i}_{query_params.metric_name}_{query_params.analysis_type}"
            DataCenter.store_data(
                store=store,
                config=config,
                data_key=data_key,
                data=pd.DataFrame(),
                error_info=f"查询失败: {str(e)}",
                query_params=query_params,
                query_title=f"进一步分析失败: {query_params.metric_name} - {query_params.analysis_type}",
            )
