import logging
from typing import Literal

from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command
from src.zego_tools import DataQueryParams

from src.nodes.dimension_analysis.base_dimension_node import BaseDimensionNode
from src.nodes.dimension_analysis.prompts import (
    appid_trend_prompt_template,
    country_trend_prompt_template,
    error_distribution_prompt_template,
    isp_trend_prompt_template,
    metric_trend_prompt_template,
    sdk_trend_prompt_template,
)
from src.state import State

logger = logging.getLogger(__name__)


async def data_query_analysis_node(
    state: State,
    config: RunnableConfig,
    *,
    store: BaseStore,
    query_params: DataQueryParams
) -> Command[Literal["aggregator"]]:
    """
    通用数据查询分析节点 - 接受DataQueryParams参数，执行对应的分析

    Args:
        state: 状态对象
        config: 运行配置
        store: 存储对象
        query_params: 数据查询参数，包含指标、分析类型等信息

    Returns:
        Command对象，指向aggregator节点
    """
    logger.info(f"🤖data_query_analysis_node working with params: {query_params.metric_name} - {query_params.analysis_type}")

    # 根据分析类型和指标类型选择对应的prompt模板和维度名称
    if query_params.metric_type == "错误码分布":
        prompt_template = error_distribution_prompt_template
        dimension_name = "错误码分布分析"
        dimension_key = "error_distribution_analysis"
    elif query_params.analysis_type == "指标趋势分析":
        prompt_template = metric_trend_prompt_template
        dimension_name = "指标趋势分析"
        dimension_key = "metric_trend_analysis"
    elif query_params.analysis_type == "SDK版本趋势分析":
        prompt_template = sdk_trend_prompt_template
        dimension_name = "SDK版本趋势分析"
        dimension_key = "sdk_trend_analysis"
    elif query_params.analysis_type == "国家维度趋势分析":
        prompt_template = country_trend_prompt_template
        dimension_name = "国家维度趋势分析"
        dimension_key = "country_trend_analysis"
    elif query_params.analysis_type == "运营商趋势分析":
        prompt_template = isp_trend_prompt_template
        dimension_name = "运营商趋势分析"
        dimension_key = "isp_trend_analysis"
    elif query_params.analysis_type == "客户维度趋势分析":
        prompt_template = appid_trend_prompt_template
        dimension_name = "客户维度趋势分析"
        dimension_key = "appid_trend_analysis"
    else:
        logger.error(f"未知的分析类型: {query_params.analysis_type}")
        raise ValueError(f"未知的分析类型: {query_params.analysis_type}")

    # 创建BaseDimensionNode实例并执行分析
    dimension_node = BaseDimensionNode(
        dimension_key=dimension_key,
        dimension_name=dimension_name,
        prompt_template=prompt_template,
        query_params=query_params  # 传递查询参数
    )

    return await dimension_node.execute(state, config, store=store)


# 为了向后兼容，保留原有的节点函数，但它们现在都使用预查询数据
# 这些函数将在dispatcher使用新的分发逻辑后逐步废弃

async def metric_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """指标趋势分析节点 - 兼容性函数，使用预查询数据"""
    from src.nodes.common.dimension_config import DimensionManager

    dimension_config = DimensionManager.get_dimension_by_key("metric_trend_analysis")

    # 根据state中的信息选择prompt模板
    if hasattr(state, "query_params") and state.query_params and state.query_params.metric_type == "错误码分布":
        prompt_template = error_distribution_prompt_template
        dimension_name = "错误码趋势分析"
    else:
        prompt_template = metric_trend_prompt_template
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=prompt_template,
    ).execute(state, config, store=store)


async def error_distribution_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """错误码分布分析节点 - 兼容性函数，重定向到metric_trend_analysis_node"""
    return await metric_trend_analysis_node(state, config, store=store)


async def sdk_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """SDK版本趋势分析节点 - 兼容性函数，使用预查询数据"""
    from src.nodes.common.dimension_config import DimensionManager

    dimension_config = DimensionManager.get_dimension_by_key("sdk_trend_analysis")

    # 根据state中的信息选择显示名称
    if hasattr(state, "query_params") and state.query_params and state.query_params.metric_type == "错误码分布":
        dimension_name = f"SDK版本错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=sdk_trend_prompt_template,
    ).execute(state, config, store=store)


async def country_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """国家维度趋势分析节点 - 兼容性函数，使用预查询数据"""
    from src.nodes.common.dimension_config import DimensionManager

    dimension_config = DimensionManager.get_dimension_by_key("country_trend_analysis")

    # 根据state中的信息选择显示名称
    if hasattr(state, "query_params") and state.query_params and state.query_params.metric_type == "错误码分布":
        dimension_name = f"国家维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=country_trend_prompt_template,
    ).execute(state, config, store=store)


async def isp_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """运营商趋势分析节点 - 兼容性函数，使用预查询数据"""
    from src.nodes.common.dimension_config import DimensionManager

    dimension_config = DimensionManager.get_dimension_by_key("isp_trend_analysis")

    # 根据state中的信息选择显示名称
    if hasattr(state, "query_params") and state.query_params and state.query_params.metric_type == "错误码分布":
        dimension_name = f"运营商维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=isp_trend_prompt_template,
    ).execute(state, config, store=store)


async def appid_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """客户维度趋势分析节点 - 兼容性函数，使用预查询数据"""
    from src.nodes.common.dimension_config import DimensionManager

    dimension_config = DimensionManager.get_dimension_by_key("appid_trend_analysis")

    # 根据state中的信息选择显示名称
    if hasattr(state, "query_params") and state.query_params and state.query_params.metric_type == "错误码分布":
        dimension_name = f"客户维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=appid_trend_prompt_template,
    ).execute(state, config, store=store)
